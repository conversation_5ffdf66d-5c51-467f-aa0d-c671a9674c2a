@import "tailwindcss";
@import "tw-animate-css";
@import "./animations.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Responsive improvements for CopilotKit */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom responsive styles for CopilotKit components */
@media (max-width: 768px) {
  /* Ensure CopilotKit sidebar is mobile-friendly - Enhanced contrast */
  [data-copilot-sidebar] {
    width: 100vw !important;
    max-width: 100vw !important;
    height: 100vh !important;
    height: 100dvh !important; /* Dynamic viewport height for mobile */
    background-color: #f8fafc !important; /* Light gray background for better contrast */
  }

  /* Chat container optimizations */
  [data-copilot-chat] {
    height: 100vh !important;
    height: 100dvh !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Chat messages area */
  [data-copilot-messages] {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 0.75rem !important;
    scroll-behavior: smooth !important;
  }

  /* Individual message bubbles - Improved contrast */
  [data-copilot-message] {
    max-width: 85% !important;
    margin-bottom: 0.75rem !important;
    padding: 0.75rem 1rem !important;
    border-radius: 1rem !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
    font-weight: 400 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  /* User messages (right aligned) - Enhanced contrast */
  [data-copilot-message][data-role="user"] {
    margin-left: auto !important;
    background-color: #2563eb !important; /* Darker blue for better contrast */
    color: #ffffff !important;
    border: 1px solid #1d4ed8 !important;
  }

  /* Assistant messages (left aligned) - Enhanced contrast */
  [data-copilot-message][data-role="assistant"] {
    margin-right: auto !important;
    background-color: #ffffff !important; /* Pure white background */
    color: #111827 !important; /* Darker text for better contrast */
    border: 1px solid #d1d5db !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }

  /* Chat input area - Enhanced contrast */
  [data-copilot-input-area] {
    padding: 1rem !important;
    border-top: 1px solid #d1d5db !important;
    background-color: #ffffff !important; /* Pure white background */
    flex-shrink: 0 !important;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  /* Chat input field - Enhanced contrast */
  [data-copilot-chat-input],
  [data-copilot-textarea] {
    font-size: 16px !important; /* Prevents zoom on iOS */
    min-height: 44px !important;
    padding: 0.75rem 1rem !important;
    border-radius: 1.5rem !important;
    border: 2px solid #d1d5db !important;
    background-color: #ffffff !important;
    color: #111827 !important; /* Dark text for better contrast */
    resize: none !important;
    max-height: 120px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  /* Chat input focus state */
  [data-copilot-chat-input]:focus,
  [data-copilot-textarea]:focus {
    border-color: #2563eb !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
  }

  /* Send button */
  [data-copilot-send-button] {
    min-width: 44px !important;
    min-height: 44px !important;
    border-radius: 50% !important;
    margin-left: 0.5rem !important;
    background-color: #3b82f6 !important;
    color: white !important;
  }

  /* Improve touch targets */
  button {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  /* Better mobile spacing */
  .copilot-chat {
    padding: 0 !important;
  }

  /* Typing indicator - Enhanced contrast */
  [data-copilot-typing] {
    padding: 0.5rem 1rem !important;
    margin: 0.5rem 0.75rem !important;
    background-color: #ffffff !important; /* White background for better contrast */
    color: #6b7280 !important; /* Gray text for typing indicator */
    border: 1px solid #e5e7eb !important;
    border-radius: 1rem !important;
    max-width: 85% !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  /* Scroll improvements */
  [data-copilot-messages]::-webkit-scrollbar {
    width: 4px !important;
  }

  [data-copilot-messages]::-webkit-scrollbar-thumb {
    background-color: #d1d5db !important;
    border-radius: 2px !important;
  }
}

/* Tablet responsive styles (768px - 1024px) - Enhanced contrast */
@media (min-width: 768px) and (max-width: 1024px) {
  [data-copilot-sidebar] {
    width: 400px !important;
    max-width: 50vw !important;
    background-color: #ffffff !important; /* White background for tablets */
    border-left: 1px solid #e5e7eb !important;
  }

  [data-copilot-message] {
    max-width: 75% !important;
    font-size: 15px !important;
  }

  [data-copilot-chat-input],
  [data-copilot-textarea] {
    font-size: 15px !important;
  }

  /* Enhanced message contrast for tablets */
  [data-copilot-message][data-role="assistant"] {
    background-color: #ffffff !important;
    border: 1px solid #d1d5db !important;
  }

  [data-copilot-message][data-role="user"] {
    background-color: #2563eb !important;
    border: 1px solid #1d4ed8 !important;
  }
}

/* General CopilotKit improvements for all screen sizes - Enhanced contrast */
[data-copilot-sidebar] {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  z-index: 1000 !important;
  background-color: #ffffff !important; /* Solid white background for better contrast */
}

/* Chat header improvements - Enhanced contrast */
[data-copilot-header] {
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%) !important; /* Darker gradient for better contrast */
  color: #ffffff !important;
  padding: 1rem !important;
  font-weight: 600 !important;
  border-bottom: 1px solid #e5e7eb !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Chat messages area - Enhanced background */
[data-copilot-messages] {
  background-color: #f8fafc !important; /* Light background for message area */
}

/* General message improvements for all screens */
[data-copilot-message] {
  font-weight: 400 !important;
  letter-spacing: 0.01em !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

/* Message animations */
[data-copilot-message] {
  animation: messageSlideIn 0.3s ease-out !important;
  transform-origin: bottom !important;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Focus states for accessibility */
[data-copilot-chat-input]:focus,
[data-copilot-textarea]:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  border-color: #3b82f6 !important;
}

/* Loading states */
[data-copilot-loading] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 1rem !important;
}

/* Improve backdrop blur support - Enhanced contrast */
@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur-md {
    backdrop-filter: blur(12px);
  }

  /* Reduce backdrop blur for better readability */
  [data-copilot-sidebar] {
    backdrop-filter: blur(8px) !important; /* Reduced blur for better text readability */
    background-color: rgba(255, 255, 255, 0.98) !important; /* Less transparent for better contrast */
  }
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(10px)) {
  [data-copilot-sidebar] {
    background-color: #ffffff !important; /* Solid white fallback */
  }
}
