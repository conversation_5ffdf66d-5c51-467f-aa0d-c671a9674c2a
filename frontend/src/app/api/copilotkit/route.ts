import { NextRequest } from "next/server";
import {
  CopilotRuntime,
  copilotRuntimeNextJSAppRouterEndpoint,
  langGraphPlatformEndpoint,
  OpenAIAdapter,
} from "@copilotkit/runtime";

// Create a simple service adapter
const serviceAdapter = new OpenAIAdapter();

// Function to create runtime with dynamic model support
function createRuntime(request: NextRequest) {
  // Get selected model from cookie or header
  const selectedModel = request.cookies.get('copilotkit-model')?.value ||
                       request.headers.get('x-copilotkit-model') ||
                       'openai/gpt-3.5-turbo'; // default

  // Store the selected model in a global variable that the agent can access
  (globalThis as { selectedModel?: string }).selectedModel = selectedModel;

  return new CopilotRuntime({
    remoteEndpoints: [
      langGraphPlatformEndpoint({
        deploymentUrl: process.env.LANGGRAPH_DEPLOYMENT_URL || "http://127.0.0.1:8123",
        langsmithApiKey: process.env.LANGSMITH_API_KEY || "",
        agents: [{
          name: "sample_agent",
          description: "A helpful AI assistant that can help with various tasks."
        }]
      }),
    ],
  });
}

export const POST = async (req: NextRequest) => {
  // Create runtime with dynamic model selection
  const runtime = createRuntime(req);

  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter,
    endpoint: "/api/copilotkit",
  });

  return handleRequest(req);
};
