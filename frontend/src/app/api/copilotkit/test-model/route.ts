import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { model, message } = await request.json();

    if (!model) {
      return NextResponse.json(
        { error: 'Model is required' },
        { status: 400 }
      );
    }

    // Test the model by sending a simple request to the LangGraph agent
    const langGraphUrl = process.env.LANGGRAPH_DEPLOYMENT_URL || 'http://127.0.0.1:8123';

    // First, create a thread
    const threadResponse = await fetch(`${langGraphUrl}/threads`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });

    if (!threadResponse.ok) {
      return NextResponse.json(
        { error: 'Failed to create thread' },
        { status: 500 }
      );
    }

    const thread = await threadResponse.json();
    const threadId = thread.thread_id;

    // Now run the assistant
    const runPayload = {
      assistant_id: "sample_agent",
      input: {
        messages: [
          {
            role: 'user',
            content: message || 'Test connection'
          }
        ],
        proverbs: [],
        copilotkit: {
          actions: []
        }
      },
      config: {
        configurable: {
          model_id: model
        }
      }
    };

    const response = await fetch(`${langGraphUrl}/threads/${threadId}/runs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(runPayload),
    });

    if (response.ok) {
      const runData = await response.json();
      return NextResponse.json({
        success: true,
        message: `Model ${model} is working correctly`,
        model,
        runId: runData.run_id,
        threadId: threadId
      });
    } else {
      const errorText = await response.text();
      console.error('LangGraph API error:', errorText);
      return NextResponse.json(
        { error: `Model test failed: ${errorText}` },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error testing model:', error);
    return NextResponse.json(
      { error: `Failed to test model: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
