"use client";

import { useCoAgent, useCopilotAction } from "@copilotkit/react-core";
import { CopilotKitCSSProperties, CopilotSidebar } from "@copilotkit/react-ui";
import { useState } from "react";
import ModelSelector, { AIModel, AI_MODELS } from "../../components/ModelSelector";
import ModelStatus from "../../components/ModelStatus";
import MobileMenu from "../../components/MobileMenu";
import MobileChatEnhancements from "../../components/MobileChatEnhancements";
import TouchOptimizations from "../../components/TouchOptimizations";
import useModelManager from "../../hooks/useModelManager";

export default function CopilotKitPage() {
  const [themeColor, setThemeColor] = useState("#6366f1");
  const [showModelSelector, setShowModelSelector] = useState(false);

  // Model management
  const {
    currentModel,
    isConnected,
    lastResponse,
    error,
    isLoading,
    changeModel,
    clearError
  } = useModelManager();

  // 🪁 Frontend Actions: https://docs.copilotkit.ai/guides/frontend-actions
  useCopilotAction({
    name: "setThemeColor",
    parameters: [{
      name: "themeColor",
      description: "The theme color to set. Make sure to pick nice colors.",
      required: true,
    }],
    handler({ themeColor }) {
      setThemeColor(themeColor);
    },
  });

  // Action to change AI model
  useCopilotAction({
    name: "changeAIModel",
    parameters: [{
      name: "modelName",
      description: "The name of the AI model to switch to (e.g., 'GPT-4', 'Claude', 'Llama')",
      required: true,
    }],
    handler({ modelName }) {
      // Find model by name and switch to it
      const model = AI_MODELS.find(m =>
        m.name.toLowerCase().includes(modelName.toLowerCase()) ||
        m.id.toLowerCase().includes(modelName.toLowerCase())
      );
      if (model) {
        changeModel(model);
      }
    },
  });

  return (
    <main style={{ "--copilot-kit-primary-color": themeColor } as CopilotKitCSSProperties}>
      <YourMainContent
        themeColor={themeColor}
        currentModel={currentModel}
        isConnected={isConnected}
        lastResponse={lastResponse}
        error={error}
        isLoading={isLoading}
        onModelChange={changeModel}
        onClearError={clearError}
        showModelSelector={showModelSelector}
        onToggleModelSelector={() => setShowModelSelector(!showModelSelector)}
      />
      <CopilotSidebar
        clickOutsideToClose={true}
        defaultOpen={false}
        labels={{
          title: "Assistant IA",
          initial: "👋 Salut ! Vous chattez avec un agent IA. Cet agent dispose de plusieurs outils pour vous aider.\n\nPar exemple, vous pouvez essayer :\n- **Outils Frontend** : \"Changer le thème en orange\"\n- **État partagé** : \"Écris un proverbe sur l&apos;IA\"\n- **Interface générative** : \"Météo à Paris\"\n- **Sélection de modèle** : \"Changer pour GPT-4\" ou \"Passer à Claude\"\n\nEn interagissant avec l&apos;agent, vous verrez l&apos;interface se mettre à jour en temps réel pour refléter l&apos;**état**, les **appels d&apos;outils** et les **progrès** de l&apos;agent."
        }}
      />

      {/* Améliorations mobiles pour le chat */}
      <MobileChatEnhancements isOpen={true} />

      {/* Optimisations tactiles */}
      <TouchOptimizations />
    </main>
  );
}

// State of the agent, make sure this aligns with your agent's state.
type AgentState = {
  proverbs: string[];
}

interface YourMainContentProps {
  themeColor: string;
  currentModel: AIModel;
  isConnected: boolean;
  lastResponse?: Date;
  error?: string | null;
  isLoading: boolean;
  onModelChange: (model: AIModel) => Promise<void>;
  onClearError: () => void;
  showModelSelector: boolean;
  onToggleModelSelector: () => void;
}

function YourMainContent({
  themeColor,
  currentModel,
  isConnected,
  lastResponse,
  error,
  isLoading,
  onModelChange,
  onClearError,
  showModelSelector,
  onToggleModelSelector
}: YourMainContentProps) {
  // 🪁 Shared State: https://docs.copilotkit.ai/coagents/shared-state
  const {state, setState} = useCoAgent<AgentState>({
    name: "sample_agent",
    initialState: {
      proverbs: [
        "CopilotKit may be new, but its the best thing since sliced bread.",
      ],
    },
  })

  // 🪁 Frontend Actions: https://docs.copilotkit.ai/coagents/frontend-actions
  useCopilotAction({
    name: "addProverb",
    parameters: [{
      name: "proverb",
      description: "The proverb to add. Make it witty, short and concise.",
      required: true,
    }],
    handler: ({ proverb }) => {
      setState({
        ...state,
        proverbs: [...state.proverbs, proverb],
      });
    },
  });

  //🪁 Generative UI: https://docs.copilotkit.ai/coagents/generative-ui
  useCopilotAction({
    name: "getWeather",
    description: "Get the weather for a given location.",
    available: "disabled",
    parameters: [
      { name: "location", type: "string", required: true },
    ],
    render: ({ args }) => {
      return <WeatherCard location={args.location} themeColor={themeColor} />
    },
  });

  return (
    <div
      style={{ backgroundColor: themeColor }}
      className="min-h-screen w-screen flex justify-center items-center flex-col transition-colors duration-300 p-2 sm:p-4 lg:p-6"
    >
      {/* Mobile Menu */}
      <MobileMenu
        currentModel={currentModel}
        isConnected={isConnected}
        onToggleModelSelector={onToggleModelSelector}
      />

      {/* Model Selector Panel - Responsive (Desktop) */}
      <div className="fixed top-2 right-2 sm:top-4 sm:right-4 z-50 hidden md:block">
        <button
          onClick={onToggleModelSelector}
          className="bg-white/20 backdrop-blur-md text-white px-2 py-2 sm:px-4 sm:py-2 rounded-lg hover:bg-white/30 transition-all flex items-center gap-1 sm:gap-2 text-sm sm:text-base"
        >
          <span className="text-lg sm:text-xl">{currentModel.icon}</span>
          <span className="font-medium hidden sm:inline">{currentModel.name}</span>
          <span className="font-medium sm:hidden text-xs">{currentModel.name.split(' ')[0]}</span>
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'} ${isConnected ? 'animate-pulse' : ''}`} />
        </button>

        {showModelSelector && (
          <>
            {/* Desktop Model Selector */}
            <div className="absolute top-full right-0 mt-2 w-80 sm:w-96 max-w-[calc(100vw-1rem)] hidden md:block">
              <div className="bg-white/95 backdrop-blur-md rounded-lg shadow-xl p-3 sm:p-4 max-h-[80vh] overflow-y-auto">
                <div className="mb-3 sm:mb-4">
                  <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-2">Sélection du modèle IA</h3>
                  <ModelStatus
                    model={currentModel}
                    isConnected={isConnected}
                    lastResponse={lastResponse}
                    error={error}
                  />
                </div>

                <div className="mb-3 sm:mb-4">
                  <ModelSelector
                    onModelChange={onModelChange}
                    currentModel={currentModel.id}
                    disabled={isLoading}
                  />
                </div>

                {error && (
                  <div className="mb-3 sm:mb-4">
                    <button
                      onClick={onClearError}
                      className="w-full bg-red-100 hover:bg-red-200 text-red-700 px-3 py-2 rounded text-sm transition-colors"
                    >
                      Effacer l&apos;erreur
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Mobile Model Selector - Full Screen */}
            <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm md:hidden">
              <div className="fixed inset-x-0 bottom-0 bg-white rounded-t-2xl shadow-xl max-h-[90vh] overflow-y-auto">
                <div className="p-4">
                  {/* Header with close button */}
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-800">Sélection du modèle IA</h3>
                    <button
                      onClick={onToggleModelSelector}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <span className="text-gray-500 text-xl">✕</span>
                    </button>
                  </div>

                  <div className="mb-4">
                    <ModelStatus
                      model={currentModel}
                      isConnected={isConnected}
                      lastResponse={lastResponse}
                      error={error}
                    />
                  </div>

                  <div className="mb-4">
                    <ModelSelector
                      onModelChange={onModelChange}
                      currentModel={currentModel.id}
                      disabled={isLoading}
                    />
                  </div>

                  {error && (
                    <div className="mb-4">
                      <button
                        onClick={onClearError}
                        className="w-full bg-red-100 hover:bg-red-200 text-red-700 px-3 py-2 rounded text-sm transition-colors"
                      >
                        Effacer l&apos;erreur
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Main Content - Responsive */}
      <div className="bg-white/20 backdrop-blur-md p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl shadow-xl max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl w-full mx-2 sm:mx-4">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 text-center">Proverbs</h1>
        <p className="text-gray-200 text-center italic mb-4 sm:mb-6 text-sm sm:text-base">
          This is a demonstrative page, but it could be anything you want! 🪁
          <br className="hidden sm:block" />
          <span className="text-xs sm:text-sm block sm:inline mt-1 sm:mt-0">
            Modèle actuel: <span className="font-medium">{currentModel.name}</span> {isConnected ? '✅' : '❌'}
          </span>
        </p>
        <hr className="border-white/20 my-4 sm:my-6" />
        <div className="flex flex-col gap-2 sm:gap-3">
          {state.proverbs?.map((proverb, index) => (
            <div
              key={index}
              className="bg-white/15 p-3 sm:p-4 rounded-lg sm:rounded-xl text-white relative group hover:bg-white/20 transition-all"
            >
              <p className="pr-8 sm:pr-10 text-sm sm:text-base leading-relaxed">{proverb}</p>
              <button
                onClick={() => setState({
                  ...state,
                  proverbs: state.proverbs?.filter((_, i) => i !== index),
                })}
                className="absolute right-2 sm:right-3 top-2 sm:top-3 opacity-0 group-hover:opacity-100 transition-opacity
                  bg-red-500 hover:bg-red-600 text-white rounded-full h-5 w-5 sm:h-6 sm:w-6 flex items-center justify-center text-xs sm:text-sm"
              >
                ✕
              </button>
            </div>
          ))}
        </div>
        {state.proverbs?.length === 0 && <p className="text-center text-white/80 italic my-6 sm:my-8 text-sm sm:text-base">
          No proverbs yet. Ask the assistant to add some!
        </p>}
      </div>
    </div>
  );
}

// Simple sun icon for the weather card
function SunIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-14 h-14 text-yellow-200">
      <circle cx="12" cy="12" r="5" />
      <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" strokeWidth="2" stroke="currentColor" />
    </svg>
  );
}

// Weather card component where the location and themeColor are based on what the agent
// sets via tool calls - Responsive version
function WeatherCard({ location, themeColor }: { location?: string, themeColor: string }) {
  return (
    <div
    style={{ backgroundColor: themeColor }}
    className="rounded-lg sm:rounded-xl shadow-xl mt-4 sm:mt-6 mb-4 max-w-xs sm:max-w-md w-full mx-auto"
  >
    <div className="bg-white/20 p-3 sm:p-4 w-full">
      <div className="flex items-center justify-between">
        <div className="min-w-0 flex-1">
          <h3 className="text-lg sm:text-xl font-bold text-white capitalize truncate">{location}</h3>
          <p className="text-white text-sm sm:text-base">Current Weather</p>
        </div>
        <div className="flex-shrink-0">
          <SunIcon />
        </div>
      </div>

      <div className="mt-3 sm:mt-4 flex items-end justify-between">
        <div className="text-2xl sm:text-3xl font-bold text-white">70°</div>
        <div className="text-xs sm:text-sm text-white">Clear skies</div>
      </div>

      <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-white">
        <div className="grid grid-cols-3 gap-1 sm:gap-2 text-center">
          <div>
            <p className="text-white text-xs">Humidity</p>
            <p className="text-white font-medium text-sm">45%</p>
          </div>
          <div>
            <p className="text-white text-xs">Wind</p>
            <p className="text-white font-medium text-sm">5 mph</p>
          </div>
          <div>
            <p className="text-white text-xs">Feels Like</p>
            <p className="text-white font-medium text-sm">72°</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  );
}
