"use client";

import React, { useState, useEffect } from 'react';

export interface AIModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  pricing: 'free' | 'cheap' | 'moderate' | 'premium';
  performance: 'basic' | 'good' | 'excellent' | 'premium';
  supportsTools: boolean;
  contextWindow: string;
  icon: string;
}

export const AI_MODELS: AIModel[] = [
  {
    id: "meta-llama/llama-3.2-3b-instruct:free",
    name: "Llama 3.2 3B",
    provider: "Meta",
    description: "Modèle gratuit, rapide pour les tâches simples",
    pricing: "free",
    performance: "basic",
    supportsTools: false,
    contextWindow: "128K",
    icon: "🦙"
  },
  {
    id: "openai/gpt-3.5-turbo",
    name: "GPT-3.5 Turbo",
    provider: "OpenAI",
    description: "Équilibre parfait entre coût et performance",
    pricing: "cheap",
    performance: "good",
    supportsTools: true,
    contextWindow: "16K",
    icon: "🤖"
  },
  {
    id: "openai/gpt-4",
    name: "GPT-4",
    provider: "OpenAI",
    description: "Modèle très performant pour les tâches complexes",
    pricing: "premium",
    performance: "excellent",
    supportsTools: true,
    contextWindow: "128K",
    icon: "🧠"
  },
  {
    id: "anthropic/claude-3-haiku-20240307",
    name: "Claude 3 Haiku",
    provider: "Anthropic",
    description: "Rapide et efficace pour la plupart des tâches",
    pricing: "cheap",
    performance: "good",
    supportsTools: true,
    contextWindow: "200K",
    icon: "🎭"
  },
  {
    id: "anthropic/claude-3-5-sonnet-20241022",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    description: "Le plus avancé d'Anthropic, excellent raisonnement",
    pricing: "premium",
    performance: "premium",
    supportsTools: true,
    contextWindow: "200K",
    icon: "🎨"
  },
  {
    id: "google/gemini-pro",
    name: "Gemini Pro",
    provider: "Google",
    description: "Alternative performante de Google",
    pricing: "moderate",
    performance: "excellent",
    supportsTools: true,
    contextWindow: "32K",
    icon: "💎"
  }
];

interface ModelSelectorProps {
  onModelChange: (model: AIModel) => void;
  currentModel?: string;
  disabled?: boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  onModelChange,
  currentModel,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState<AIModel>(
    AI_MODELS.find(m => m.id === currentModel) || AI_MODELS[1] // Default to GPT-3.5
  );
  const [error, setError] = useState<string | null>(null);

  // Load saved model from localStorage on mount
  useEffect(() => {
    const savedModelId = localStorage.getItem('copilotkit-selected-model');
    if (savedModelId) {
      const savedModel = AI_MODELS.find(m => m.id === savedModelId);
      if (savedModel) {
        setSelectedModel(savedModel);
        onModelChange(savedModel);
      }
    }
  }, [onModelChange]);

  const handleModelSelect = (model: AIModel) => {
    setSelectedModel(model);
    setIsOpen(false);
    setError(null);
    
    // Save to localStorage
    localStorage.setItem('copilotkit-selected-model', model.id);
    
    // Notify parent component
    onModelChange(model);
  };

  const getPricingColor = (pricing: string) => {
    switch (pricing) {
      case 'free': return 'text-green-500';
      case 'cheap': return 'text-blue-500';
      case 'moderate': return 'text-yellow-500';
      case 'premium': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getPricingLabel = (pricing: string) => {
    switch (pricing) {
      case 'free': return 'Gratuit';
      case 'cheap': return 'Économique';
      case 'moderate': return 'Modéré';
      case 'premium': return 'Premium';
      default: return 'Inconnu';
    }
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'basic': return 'text-gray-500';
      case 'good': return 'text-blue-500';
      case 'excellent': return 'text-green-500';
      case 'premium': return 'text-purple-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="relative w-full max-w-md">
      {/* Error display */}
      {error && (
        <div className="mb-2 p-2 bg-red-100 border border-red-300 rounded-md flex items-center gap-2 text-red-700 text-sm">
          <span>⚠️</span>
          {error}
        </div>
      )}

      {/* Main selector button - Responsive */}
      <button
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full p-2 sm:p-3 bg-white border border-gray-300 rounded-lg shadow-sm
          flex items-center justify-between
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 cursor-pointer'}
          transition-all duration-200
        `}
      >
        <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
          <span className="text-lg sm:text-xl flex-shrink-0">{selectedModel.icon}</span>
          <div className="text-left min-w-0 flex-1">
            <div className="font-medium text-gray-900 text-sm sm:text-base truncate">{selectedModel.name}</div>
            <div className="text-xs sm:text-sm text-gray-500 flex items-center gap-1 sm:gap-2 flex-wrap">
              <span className={getPricingColor(selectedModel.pricing)}>
                {getPricingLabel(selectedModel.pricing)}
              </span>
              <span className="hidden sm:inline">•</span>
              <span className={`hidden sm:inline ${getPerformanceColor(selectedModel.performance)}`}>
                {selectedModel.performance}
              </span>
              {selectedModel.supportsTools && (
                <>
                  <span className="hidden md:inline">•</span>
                  <span className="text-green-600 hidden md:inline">🛠️ Outils</span>
                </>
              )}
            </div>
          </div>
        </div>
        <span
          className={`text-gray-400 transition-transform duration-200 flex-shrink-0 text-sm ${
            isOpen ? 'rotate-180' : ''
          }`}
        >
          ▼
        </span>
      </button>

      {/* Dropdown menu - Responsive */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-80 sm:max-h-96 overflow-y-auto">
          {AI_MODELS.map((model) => (
            <button
              key={model.id}
              onClick={() => handleModelSelect(model)}
              className={`
                w-full p-2 sm:p-3 text-left hover:bg-gray-50 transition-colors duration-150
                ${selectedModel.id === model.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''}
                border-b border-gray-100 last:border-b-0
              `}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                  <span className="text-lg sm:text-xl flex-shrink-0">{model.icon}</span>
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-gray-900 flex items-center gap-2 text-sm sm:text-base">
                      <span className="truncate">{model.name}</span>
                      {selectedModel.id === model.id && (
                        <span className="text-blue-500 flex-shrink-0">✓</span>
                      )}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 line-clamp-2 sm:line-clamp-none">{model.description}</div>
                    <div className="text-xs text-gray-500 flex items-center gap-1 sm:gap-2 mt-1 flex-wrap">
                      <span className={getPricingColor(model.pricing)}>
                        {getPricingLabel(model.pricing)}
                      </span>
                      <span className="hidden sm:inline">•</span>
                      <span className="hidden sm:inline">{model.contextWindow}</span>
                      {model.supportsTools && (
                        <>
                          <span className="hidden md:inline">•</span>
                          <span className="text-green-600 hidden md:inline">Outils supportés</span>
                          <span className="text-green-600 md:hidden">🛠️</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* Overlay to close dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default ModelSelector;
