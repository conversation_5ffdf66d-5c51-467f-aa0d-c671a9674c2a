"use client";

import React, { useEffect } from 'react';

export const TouchOptimizations: React.FC = () => {
  
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Optimisations tactiles pour mobile
    const optimizeTouchInteractions = () => {
      // Améliorer les zones de clic pour tous les boutons
      const buttons = document.querySelectorAll('button');
      buttons.forEach((button) => {
        if (window.innerWidth < 768) {
          button.style.minHeight = '44px';
          button.style.minWidth = '44px';
          button.style.padding = button.style.padding || '0.5rem';
        }
      });

      // Optimiser les liens
      const links = document.querySelectorAll('a');
      links.forEach((link) => {
        if (window.innerWidth < 768) {
          link.style.minHeight = '44px';
          link.style.display = 'inline-flex';
          link.style.alignItems = 'center';
          link.style.padding = link.style.padding || '0.5rem';
        }
      });

      // Améliorer les champs de saisie
      const inputs = document.querySelectorAll('input, textarea');
      inputs.forEach((input) => {
        if (window.innerWidth < 768) {
          const inputEl = input as HTMLInputElement;
          inputEl.style.fontSize = '16px'; // Prévient le zoom iOS
          inputEl.style.minHeight = '44px';
          inputEl.style.padding = '0.75rem';
        }
      });

      // Optimiser les éléments CopilotKit spécifiquement
      const copilotElements = document.querySelectorAll('[data-copilot-chat-input], [data-copilot-textarea], [data-copilot-send-button]');
      copilotElements.forEach((element) => {
        if (window.innerWidth < 768) {
          const el = element as HTMLElement;
          if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA') {
            el.style.fontSize = '16px';
            el.style.minHeight = '44px';
            el.style.padding = '0.75rem 1rem';
          } else if (el.tagName === 'BUTTON') {
            el.style.minHeight = '44px';
            el.style.minWidth = '44px';
          }
        }
      });
    };

    // Gérer les gestes de swipe pour fermer la sidebar
    let startX = 0;
    let startY = 0;
    let isSwipeGesture = false;

    const handleTouchStart = (e: TouchEvent) => {
      if (window.innerWidth >= 768) return;
      
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
      isSwipeGesture = false;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (window.innerWidth >= 768) return;
      
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;
      const diffX = startX - currentX;
      const diffY = startY - currentY;

      // Détecter un swipe horizontal
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        isSwipeGesture = true;
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (window.innerWidth >= 768 || !isSwipeGesture) return;
      
      const endX = e.changedTouches[0].clientX;
      const diffX = startX - endX;
      
      // Swipe vers la droite pour fermer la sidebar (si elle est ouverte)
      if (diffX < -100) {
        const sidebar = document.querySelector('[data-copilot-sidebar]') as HTMLElement;
        if (sidebar && sidebar.style.display !== 'none') {
          // Déclencher la fermeture de la sidebar
          const closeButton = sidebar.querySelector('[data-copilot-close]') as HTMLButtonElement;
          if (closeButton) {
            closeButton.click();
          }
        }
      }
    };

    // Améliorer le scroll momentum sur iOS
    const improveScrolling = () => {
      const scrollableElements = document.querySelectorAll('[data-copilot-messages], .overflow-y-auto, .overflow-auto');
      scrollableElements.forEach((element) => {
        const el = element as HTMLElement;
        el.style.webkitOverflowScrolling = 'touch';
        el.style.overscrollBehavior = 'contain';
      });
    };

    // Prévenir le zoom sur double-tap
    const preventDoubleTabZoom = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    // Appliquer les optimisations
    optimizeTouchInteractions();
    improveScrolling();

    // Ajouter les event listeners
    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });
    document.addEventListener('touchstart', preventDoubleTabZoom, { passive: false });

    // Observer pour réappliquer les optimisations
    const observer = new MutationObserver(() => {
      setTimeout(optimizeTouchInteractions, 100);
      setTimeout(improveScrolling, 100);
    });
    observer.observe(document.body, { childList: true, subtree: true });

    // Gérer le redimensionnement
    const handleResize = () => {
      optimizeTouchInteractions();
      improveScrolling();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
      document.removeEventListener('touchstart', preventDoubleTabZoom);
      window.removeEventListener('resize', handleResize);
      observer.disconnect();
    };
  }, []);

  // Ajouter des styles CSS dynamiques pour les optimisations tactiles
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const style = document.createElement('style');
    style.textContent = `
      /* Optimisations tactiles supplémentaires */
      @media (max-width: 768px) {
        /* Améliorer la sélection de texte */
        [data-copilot-message] {
          user-select: text !important;
          -webkit-user-select: text !important;
        }

        /* Améliorer les transitions tactiles */
        button, a, [role="button"] {
          transition: background-color 0.15s ease, transform 0.15s ease !important;
        }

        button:active, a:active, [role="button"]:active {
          transform: scale(0.98) !important;
          background-color: rgba(0, 0, 0, 0.1) !important;
        }

        /* Améliorer la visibilité du focus */
        *:focus {
          outline: 2px solid #3b82f6 !important;
          outline-offset: 2px !important;
        }

        /* Optimiser les animations pour les performances */
        * {
          will-change: auto !important;
        }

        [data-copilot-message] {
          will-change: transform, opacity !important;
        }

        /* Améliorer le contraste pour la lisibilité - Enhanced */
        [data-copilot-message][data-role="assistant"] {
          background-color: #ffffff !important; /* Pure white for maximum contrast */
          color: #111827 !important; /* Darker text for better readability */
          border: 1px solid #d1d5db !important;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        }

        [data-copilot-message][data-role="user"] {
          background-color: #2563eb !important; /* Darker blue for better contrast */
          color: #ffffff !important;
          border: 1px solid #1d4ed8 !important;
        }

        /* Améliorer la lisibilité du texte */
        [data-copilot-message] {
          font-weight: 400 !important;
          letter-spacing: 0.01em !important;
          line-height: 1.6 !important; /* Better line spacing for readability */
        }

        /* Enhanced input field contrast */
        [data-copilot-chat-input], [data-copilot-textarea] {
          background-color: #ffffff !important;
          color: #111827 !important;
          border: 2px solid #d1d5db !important;
        }

        /* Enhanced sidebar background */
        [data-copilot-sidebar] {
          background-color: #ffffff !important;
        }

        /* Enhanced typing indicator */
        [data-copilot-typing] {
          background-color: #ffffff !important;
          color: #6b7280 !important;
          border: 1px solid #e5e7eb !important;
        }
      }

      /* Optimisations pour les appareils avec hover */
      @media (hover: hover) {
        button:hover, a:hover, [role="button"]:hover {
          background-color: rgba(0, 0, 0, 0.05) !important;
        }
      }
    `;

    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return null;
};

export default TouchOptimizations;
