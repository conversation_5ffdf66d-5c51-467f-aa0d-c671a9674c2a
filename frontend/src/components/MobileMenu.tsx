"use client";

import React, { useState } from 'react';
import { AIModel } from './ModelSelector';

interface MobileMenuProps {
  currentModel: AIModel;
  isConnected: boolean;
  onToggleModelSelector: () => void;
}

export const MobileMenu: React.FC<MobileMenuProps> = ({
  currentModel,
  isConnected,
  onToggleModelSelector
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleModelSelectorToggle = () => {
    onToggleModelSelector();
    setIsMenuOpen(false); // Close menu after action
  };

  return (
    <div className="md:hidden">
      {/* Hamburger Menu Button */}
      <button
        onClick={toggleMenu}
        className="fixed top-2 left-2 z-50 bg-white/20 backdrop-blur-md text-white p-2 rounded-lg hover:bg-white/30 transition-all"
        aria-label="Menu"
      >
        <div className="w-6 h-6 flex flex-col justify-center items-center">
          <span className={`block w-5 h-0.5 bg-white transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-1' : ''}`} />
          <span className={`block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ${isMenuOpen ? 'opacity-0' : ''}`} />
          <span className={`block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ${isMenuOpen ? '-rotate-45 -translate-y-1' : ''}`} />
        </div>
      </button>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm">
          <div className="fixed top-0 left-0 w-80 max-w-[85vw] h-full bg-white/95 backdrop-blur-md shadow-xl">
            <div className="p-4 pt-16">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Menu</h2>
              
              {/* Model Selector Button */}
              <button
                onClick={handleModelSelectorToggle}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 p-3 rounded-lg transition-all flex items-center gap-3 mb-4"
              >
                <span className="text-xl">{currentModel.icon}</span>
                <div className="flex-1 text-left">
                  <div className="font-medium">{currentModel.name}</div>
                  <div className="text-sm text-gray-600">Changer de modèle</div>
                </div>
                <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'} ${isConnected ? 'animate-pulse' : ''}`} />
              </button>

              {/* Additional menu items can be added here */}
              <div className="border-t border-gray-200 pt-4">
                <p className="text-sm text-gray-600">
                  Modèle actuel: <span className="font-medium">{currentModel.name}</span>
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Statut: {isConnected ? '✅ Connecté' : '❌ Déconnecté'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileMenu;
