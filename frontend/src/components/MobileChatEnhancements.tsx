"use client";

import React, { useEffect } from 'react';

interface MobileChatEnhancementsProps {
  isOpen: boolean;
}

export const MobileChatEnhancements: React.FC<MobileChatEnhancementsProps> = ({ isOpen }) => {
  
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Fonction pour améliorer l'interface mobile
    const enhanceMobileChat = () => {
      // Sélecteurs pour les éléments CopilotKit
      const sidebar = document.querySelector('[data-copilot-sidebar]') as HTMLElement;
      const chatContainer = document.querySelector('[data-copilot-chat]') as HTMLElement;
      const messagesArea = document.querySelector('[data-copilot-messages]') as HTMLElement;
      const inputArea = document.querySelector('[data-copilot-input-area]') as HTMLElement;
      const chatInput = document.querySelector('[data-copilot-chat-input], [data-copilot-textarea]') as HTMLInputElement;
      const sendButton = document.querySelector('[data-copilot-send-button]') as HTMLButtonElement;

      if (window.innerWidth < 768) {
        // Améliorations pour mobile
        if (sidebar) {
          sidebar.style.position = 'fixed';
          sidebar.style.top = '0';
          sidebar.style.left = '0';
          sidebar.style.width = '100vw';
          sidebar.style.height = '100vh';
          sidebar.style.zIndex = '9999';
        }

        if (chatContainer) {
          chatContainer.style.height = '100vh';
          chatContainer.style.display = 'flex';
          chatContainer.style.flexDirection = 'column';
        }

        if (messagesArea) {
          messagesArea.style.flex = '1';
          messagesArea.style.overflowY = 'auto';
          messagesArea.style.padding = '1rem';
          messagesArea.style.scrollBehavior = 'smooth';
          
          // Améliorer le scroll automatique vers le bas
          const scrollToBottom = () => {
            messagesArea.scrollTop = messagesArea.scrollHeight;
          };
          
          // Observer pour les nouveaux messages
          const observer = new MutationObserver(scrollToBottom);
          observer.observe(messagesArea, { childList: true, subtree: true });
        }

        if (inputArea) {
          inputArea.style.padding = '1rem';
          inputArea.style.borderTop = '1px solid #e5e7eb';
          inputArea.style.backgroundColor = 'white';
          inputArea.style.flexShrink = '0';
        }

        if (chatInput) {
          chatInput.style.fontSize = '16px'; // Prévient le zoom iOS
          chatInput.style.minHeight = '44px';
          chatInput.style.padding = '0.75rem 1rem';
          chatInput.style.borderRadius = '1.5rem';
          chatInput.style.border = '2px solid #e5e7eb';
          chatInput.style.resize = 'none';
          chatInput.style.maxHeight = '120px';
          chatInput.style.width = '100%';
          chatInput.style.boxSizing = 'border-box';
        }

        if (sendButton) {
          sendButton.style.minWidth = '44px';
          sendButton.style.minHeight = '44px';
          sendButton.style.borderRadius = '50%';
          sendButton.style.marginLeft = '0.5rem';
          sendButton.style.backgroundColor = '#3b82f6';
          sendButton.style.color = 'white';
          sendButton.style.border = 'none';
          sendButton.style.cursor = 'pointer';
        }

        // Améliorer les bulles de messages
        const messages = document.querySelectorAll('[data-copilot-message]');
        messages.forEach((message) => {
          const messageEl = message as HTMLElement;
          messageEl.style.maxWidth = '85%';
          messageEl.style.marginBottom = '0.75rem';
          messageEl.style.padding = '0.75rem 1rem';
          messageEl.style.borderRadius = '1rem';
          messageEl.style.fontSize = '16px';
          messageEl.style.lineHeight = '1.5';
          
          // Différencier les messages utilisateur et assistant
          if (messageEl.getAttribute('data-role') === 'user') {
            messageEl.style.marginLeft = 'auto';
            messageEl.style.backgroundColor = '#3b82f6';
            messageEl.style.color = 'white';
          } else {
            messageEl.style.marginRight = 'auto';
            messageEl.style.backgroundColor = '#f3f4f6';
            messageEl.style.color = '#1f2937';
          }
        });
      }
    };

    // Appliquer les améliorations avec un délai pour s'assurer que les éléments sont rendus
    const timeoutId = setTimeout(enhanceMobileChat, 100);
    
    // Observer les changements dans le DOM pour réappliquer les styles
    const observer = new MutationObserver(enhanceMobileChat);
    observer.observe(document.body, { childList: true, subtree: true });

    // Gérer le redimensionnement de la fenêtre
    const handleResize = () => {
      enhanceMobileChat();
    };
    window.addEventListener('resize', handleResize);

    // Gérer l'orientation change sur mobile
    const handleOrientationChange = () => {
      setTimeout(enhanceMobileChat, 100);
    };
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      clearTimeout(timeoutId);
      observer.disconnect();
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, [isOpen]);

  // Fonction pour gérer le viewport height sur mobile
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', () => {
      setTimeout(setVH, 100);
    });

    return () => {
      window.removeEventListener('resize', setVH);
      window.removeEventListener('orientationchange', setVH);
    };
  }, []);

  return null; // Ce composant n'affiche rien, il améliore juste l'interface
};

export default MobileChatEnhancements;
